// Color Pickers for Primary & Secondary
			const primaryColorPicker = document.getElementById("primaryColorPicker");
			const secondaryColorPicker = document.getElementById(
				"secondaryColorPicker"
			);

			primaryColorPicker.addEventListener("input", (event) => {
				const newColor = event.target.value;
				document.documentElement.style.setProperty("--primary", newColor);
			});

			secondaryColorPicker.addEventListener("input", (event) => {
				const newColor = event.target.value;
				document.documentElement.style.setProperty("--secondary", newColor);
			});

			// Sections Toggle
			function customToggleSection(sectionId, radioName) {
				const section = document.getElementById(sectionId);
				const onRadio = document.querySelector(`input[name="${radioName}"][value="on"]`);
				const offRadio = document.querySelector(`input[name="${radioName}"][value="off"]`);

				function updateDisplay() {
					if (onRadio.checked) {
						section.style.display = "block";
					} else {
						section.style.display = "none";
					}
				
				    if (typeof AOS !== 'undefined') { AOS.refresh(); }
				}

				updateDisplay();

				onRadio.addEventListener("change", updateDisplay);
				offRadio.addEventListener("change", updateDisplay);
			}


			// Call for each section with matching radioName and sectionId
			customToggleSection("highlights", "highlightsSection");
			customToggleSection("photo-gallery", "photoGallery");
			customToggleSection("stats", "statsSection");
			customToggleSection("features", "featuresSection");
			customToggleSection("location", "locationSection");
			customToggleSection("downloads", "downloadsSection");
			customToggleSection("contact", "contactSection");