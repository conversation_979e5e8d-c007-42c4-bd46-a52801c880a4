name: Issue Labeled

on:
  issues:
    types: [labeled]

jobs:
  issue-labeled:
    if: github.repository == 'twbs/bootstrap'
    runs-on: ubuntu-latest
    steps:
      - name: awaiting reply
        if: github.event.label.name == 'needs-example'
        uses: actions-cool/issues-helper@v3
        with:
          actions: "create-comment"
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            Hello @${{ github.event.issue.user.login }}. Bug reports must include a **live demo** of the issue. Per our [contributing guidelines](https://github.com/twbs/bootstrap/blob/main/.github/CONTRIBUTING.md), please create a reduced test case on [CodePen](https://codepen.io/) or [StackBlitz](https://stackblitz.com/) and report back with your link, Bootstrap version, and specific browser and Operating System details.
