{"v": "5.6.6", "ip": 0, "op": 160, "fr": 60, "w": 220, "h": 43, "layers": [{"ind": 446, "nm": "surface1845", "ao": 0, "ip": 0, "op": 264, "st": 0, "ty": 4, "ks": {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [133.3, 132.56]}, "sk": {"k": 0}, "sa": {"k": 0}}, "shapes": [{"ty": "gr", "hd": false, "nm": "surface1845", "it": [{"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0.3, -0.16], [0.35, 0.01], [0.38, 0.42], [0.09, 0.27], [0, 0.3], [-0.11, 0.27], [-0.19, 0.22], [-0.25, 0.11], [-0.29, 0], [-0.3, -0.15], [-0.24, -0.23], [0, 0], [0.17, 0.12], [0.2, 0.09], [0.23, 0.04], [0.27, 0], [0.42, -0.18], [0.3, -0.3], [0.16, -0.39], [0, -0.44], [-0.16, -0.41], [-0.29, -0.3], [-0.39, -0.16], [-0.44, 0.01], [-0.27, 0.05], [-0.36, 0.29], [-0.16, 0.17]], "o": [[0, 0], [-0.25, 0.24], [-0.31, 0.16], [-0.57, 0], [-0.19, -0.21], [-0.11, -0.28], [0, -0.29], [0.09, -0.28], [0.2, -0.2], [0.27, -0.12], [0.34, -0.01], [0.3, 0.15], [0, 0], [-0.15, -0.15], [-0.18, -0.12], [-0.21, -0.09], [-0.26, -0.05], [-0.46, -0.01], [-0.39, 0.16], [-0.29, 0.31], [-0.16, 0.41], [0, 0.45], [0.15, 0.39], [0.29, 0.3], [0.41, 0.18], [0.28, 0], [0.46, -0.09], [0.19, -0.14], [0, 0]], "v": [[39, 31.31], [38.25, 30.56], [37.43, 31.16], [36.42, 31.39], [34.92, 30.72], [34.5, 30], [34.34, 29.11], [34.5, 28.25], [34.93, 27.5], [35.61, 27.02], [36.45, 26.84], [37.43, 27.07], [38.23, 27.64], [38.98, 26.81], [38.5, 26.41], [37.95, 26.09], [37.28, 25.89], [36.49, 25.82], [35.16, 26.08], [34.12, 26.78], [33.44, 27.84], [33.2, 29.12], [33.45, 30.41], [34.12, 31.45], [35.15, 32.15], [36.44, 32.41], [37.26, 32.33], [38.51, 31.75], [39.03, 31.28]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[45.43, 25.91], [44.38, 25.91], [41.57, 32.34], [42.75, 32.33], [43.4, 30.83], [46.4, 30.83], [47.05, 32.33], [48.23, 32.33], [45.43, 25.91]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[46, 29.8], [43.79, 29.8], [44.88, 27.25], [45.98, 29.8]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.12, 0.28], [0.21, 0.19], [0.28, 0.09], [0.34, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.32, 0.1], [-0.25, 0.2], [-0.13, 0.27], [0.01, 0.33]], "o": [[0, -0.3], [-0.11, -0.26], [-0.22, -0.19], [-0.33, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.33, 0], [0.31, -0.08], [0.23, -0.18], [0.14, -0.29], [0, 0]], "v": [[56.25, 28.09], [56.08, 27.21], [55.6, 26.54], [54.85, 26.11], [53.84, 25.96], [51.28, 25.96], [51.28, 32.33], [52.39, 32.33], [52.39, 30.29], [53.66, 30.29], [54.65, 30.14], [55.5, 29.72], [56.05, 29.04], [56.25, 28.09]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.25, -0.21], [0.37, 0.03], [0, 0], [0, 0], [0, 0], [-0.29, -0.21], [0.02, -0.33]], "o": [[0.01, 0.32], [-0.29, 0.23], [0, 0], [0, 0], [0, 0], [0.36, -0.02], [0.26, 0.21], [0, 0]], "v": [[55.11, 28.09], [54.73, 28.93], [53.7, 29.25], [52.39, 29.25], [52.39, 27], [53.7, 27], [54.7, 27.29], [55.08, 28.15]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.42, 25.93], [59.42, 32.31], [60.53, 32.31], [60.53, 25.93]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[66.92, 26.97], [68.95, 26.97], [68.95, 25.96], [63.75, 25.96], [63.75, 27], [65.77, 27], [65.77, 32.34], [66.9, 32.34], [66.9, 27]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[74.57, 25.89], [73.5, 25.89], [70.7, 32.31], [71.84, 32.31], [72.5, 30.81], [75.5, 30.81], [76.14, 32.31], [77.32, 32.31], [74.52, 25.89]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.13, 29.78], [72.9, 29.77], [73.98, 27.22], [75.09, 29.77]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[80.43, 32.31], [84.93, 32.31], [84.94, 31.32], [81.59, 31.32], [81.59, 25.96], [80.48, 25.96], [80.48, 32.33]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.24, -0.08], [0.29, 0], [0.28, 0.12], [0.19, 0.22], [0.09, 0.28], [0, 0.31], [-0.11, 0.28], [-0.19, 0.22], [-0.25, 0.12], [-0.28, 0], [-0.18, -0.04], [-0.15, -0.05], [-0.12, -0.09], [-0.12, -0.1], [0, 0], [0, 0], [0.18, 0.11], [0.19, 0.07], [0.22, 0.04], [0.25, 0], [0.42, -0.18], [0.3, -0.31], [0.15, -0.39], [0, -0.44], [-0.16, -0.42], [-0.28, -0.32], [-0.39, -0.16], [-0.46, 0], [-0.48, 0.19], [-0.2, 0.12], [-0.17, 0.14]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.21, 0.15], [-0.28, 0.09], [-0.3, 0], [-0.27, -0.11], [-0.2, -0.21], [-0.11, -0.29], [0, -0.3], [0.1, -0.27], [0.18, -0.2], [0.25, -0.12], [0.18, 0], [0.16, 0.04], [0.14, 0.06], [0.13, 0.09], [0, 0], [0, 0], [-0.16, -0.13], [-0.17, -0.11], [-0.21, -0.08], [-0.25, -0.04], [-0.46, 0], [-0.39, 0.18], [-0.29, 0.3], [-0.16, 0.41], [0, 0.45], [0.14, 0.4], [0.3, 0.3], [0.43, 0.17], [0.51, 0], [0.21, -0.09], [0.18, -0.11], [0, 0]], "v": [[96.45, 31.43], [96.46, 28.77], [93.74, 28.77], [93.74, 29.75], [95.37, 29.75], [95.37, 30.95], [94.69, 31.3], [93.83, 31.43], [92.95, 31.25], [92.25, 30.75], [91.81, 30], [91.65, 29.09], [91.81, 28.21], [92.25, 27.46], [92.9, 26.97], [93.72, 26.79], [94.27, 26.84], [94.73, 26.98], [95.13, 27.2], [95.5, 27.47], [96.21, 26.62], [96.21, 26.62], [95.71, 26.26], [95.16, 26], [94.52, 25.82], [93.77, 25.76], [92.45, 26.02], [91.4, 26.77], [90.73, 27.82], [90.48, 29.1], [90.72, 30.41], [91.35, 31.5], [92.39, 32.19], [93.75, 32.44], [95.25, 32.15], [95.87, 31.83], [96.4, 31.45]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [-0.19, 0.11], [-0.14, 0.16], [-0.07, 0.2], [0.01, 0.25], [0.11, 0.27], [0.2, 0.17], [0.27, 0.08], [0.34, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.21, -0.06], [0.18, -0.11], [0.14, -0.16], [0.08, -0.24], [0, -0.29], [-0.11, -0.25], [-0.22, -0.18], [-0.32, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[105.29, 32.31], [103.5, 29.92], [104.11, 29.66], [104.59, 29.25], [104.91, 28.7], [105.02, 27.95], [104.86, 27.11], [104.39, 26.47], [103.64, 26.07], [102.64, 25.93], [99.8, 25.93], [99.8, 32.3], [100.91, 32.3], [100.91, 30.05], [102.32, 30.05], [103.89, 32.3]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0.22, -0.18], [0.34, 0.02], [0, 0], [0, 0], [0, 0], [0, 0], [-0.29, -0.2], [0.02, -0.31]], "o": [[0, 0], [0, 0.29], [-0.28, 0.21], [0, 0], [0, 0], [0, 0], [0, 0], [0.35, -0.03], [0.24, 0.19], [0, 0]], "v": [[103.95, 28.02], [103.95, 28.01], [103.6, 28.76], [102.64, 29.05], [100.99, 29.05], [100.99, 27], [102.62, 27], [102.62, 27], [103.61, 27.27], [103.95, 28.07]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.16, 0.4], [0.79, 0.32], [0.86, -0.35], [0.3, -0.32], [0.16, -0.39], [-0.33, -0.82], [-0.79, -0.32], [-0.46, 0.01], [-0.51, 1.25], [0, 0.44]], "o": [[0, -0.44], [-0.32, -0.8], [-0.86, -0.35], [-0.4, 0.18], [-0.3, 0.3], [-0.33, 0.82], [0.32, 0.79], [0.43, 0.18], [1.35, 0.04], [0.16, -0.4], [0, 0]], "v": [[114.75, 29.14], [114.5, 27.86], [112.77, 26.11], [110.08, 26.11], [109.02, 26.86], [108.33, 27.91], [108.33, 30.46], [110.05, 32.21], [111.41, 32.47], [114.5, 30.44], [114.75, 29.17]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.11, -0.28], [0.2, -0.22], [0.26, -0.11], [0.62, 0.67], [0.09, 0.28], [-0.22, 0.57], [-0.2, 0.22], [-0.59, -0.01], [-0.27, -0.13], [-0.19, -0.21], [-0.1, -0.28], [0, -0.3]], "o": [[0, 0.3], [-0.09, 0.28], [-0.19, 0.21], [-0.83, 0.37], [-0.2, -0.22], [-0.22, -0.57], [0.1, -0.28], [0.4, -0.43], [0.3, 0], [0.26, 0.11], [0.2, 0.21], [0.11, 0.28], [0, 0]], "v": [[113.57, 29.14], [113.41, 30.02], [112.97, 30.77], [112.29, 31.25], [109.86, 30.75], [109.42, 30], [109.42, 28.24], [109.86, 27.49], [111.41, 26.83], [112.28, 27.02], [112.96, 27.5], [113.41, 28.25], [113.57, 29.13]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0.31, -0.37], [0.67, 0.61], [-0.03, 0.49], [0, 0], [0, 0], [0, 0], [-0.14, -0.39], [-0.25, -0.25], [-0.32, -0.11], [-0.38, 0.01], [-0.37, 0.12], [-0.24, 0.24], [-0.12, 0.34], [0.01, 0.42], [0, 0]], "o": [[0, 0], [0, 0], [0.04, 0.48], [-0.67, 0.61], [-0.31, -0.38], [0, 0], [0, 0], [0, 0], [-0.01, 0.41], [0.11, 0.33], [0.24, 0.24], [0.36, 0.12], [0.38, 0], [0.32, -0.11], [0.25, -0.26], [0.13, -0.4], [0, 0], [0, 0]], "v": [[123.43, 25.94], [122.32, 25.94], [122.32, 29.62], [121.88, 30.95], [119.51, 30.95], [119.08, 29.59], [119.08, 25.96], [117.97, 25.96], [117.97, 29.62], [118.16, 30.84], [118.71, 31.72], [119.57, 32.25], [120.68, 32.43], [121.82, 32.25], [122.68, 31.72], [123.23, 30.82], [123.43, 29.57], [123.43, 25.96]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0.11, 0.28], [0.21, 0.19], [0.28, 0.09], [0.34, -0.01], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.32, 0.1], [-0.24, 0.19], [-0.13, 0.27], [0.01, 0.33]], "o": [[0, 0], [0.01, -0.3], [-0.11, -0.26], [-0.21, -0.2], [-0.32, -0.11], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.34, 0], [0.3, -0.09], [0.23, -0.18], [0.14, -0.3], [0, 0]], "v": [[131.87, 28.08], [131.86, 28.08], [131.7, 27.2], [131.21, 26.52], [130.46, 26.09], [129.46, 25.94], [126.95, 25.94], [126.95, 32.32], [128.05, 32.32], [128.05, 30.29], [129.32, 30.29], [130.32, 30.14], [131.14, 29.72], [131.68, 29.04], [131.88, 28.09]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.24, -0.21], [0.36, 0.02], [0, 0], [0, 0], [0, 0], [-0.29, -0.21], [0.01, -0.32]], "o": [[0.01, 0.32], [-0.29, 0.22], [0, 0], [0, 0], [0, 0], [0.36, -0.02], [0.26, 0.2], [0, 0]], "v": [[130.73, 28.08], [130.36, 28.92], [129.35, 29.23], [128.04, 29.23], [128.04, 27], [129.35, 27], [130.35, 27.29], [130.75, 28.12]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[11.57, 0.2], [8.44, 0.2], [0, 19.5], [3.45, 19.5], [5.42, 14.87], [14.49, 14.87], [16.44, 19.5], [20, 19.5]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.26, 11.89], [6.66, 11.89], [9.95, 4.22]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0.94, -1.11], [1.32, 0.07], [0.93, 0.96], [-0.1, 1.47], [0, 0], [0, 0], [0, 0], [-0.4, -1.17], [-0.73, -0.75], [-0.97, -0.34], [-1.14, 0.02], [-1.1, 0.37], [-0.73, 0.73], [-0.35, 1.01], [0.02, 1.27]], "o": [[0, 0], [0, 0], [0.11, 1.45], [-0.95, 0.93], [-1.34, 0.07], [-0.93, -1.14], [0, 0], [0, 0], [0, 0], [-0.02, 1.23], [0.35, 0.99], [0.73, 0.72], [1.09, 0.37], [1.16, 0.02], [0.97, -0.34], [0.74, -0.77], [0.41, -1.21], [0, 0]], "v": [[38.71, 0.33], [35.37, 0.33], [35.37, 11.37], [34.07, 15.37], [30.52, 16.71], [26.94, 15.32], [25.64, 11.25], [25.64, 0.33], [22.3, 0.33], [22.3, 11.37], [22.88, 15], [24.52, 17.64], [27.1, 19.25], [30.46, 19.78], [33.88, 19.25], [36.46, 17.62], [38.12, 14.93], [38.71, 11.18]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[51.46, 3.45], [57.55, 3.45], [57.55, 0.33], [42, 0.33], [42, 3.45], [48.08, 3.45], [48.08, 19.5], [51.45, 19.5], [51.45, 3.45]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[73.64, 11.42], [73.64, 19.5], [76.98, 19.5], [76.99, 0.33], [73.64, 0.33], [73.64, 8.3], [64.44, 8.3], [64.44, 0.33], [61.09, 0.33], [61.09, 19.5], [64.44, 19.5], [64.44, 11.42]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[96.11, 0.33], [81.91, 0.33], [81.91, 19.5], [96.25, 19.5], [96.25, 16.5], [85.26, 16.5], [85.26, 11.34], [94.88, 11.34], [94.88, 8.34], [85.26, 8.34], [85.26, 3.37], [96.11, 3.37]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[113.64, 13.59], [103.39, 0.33], [100.27, 0.33], [100.27, 19.5], [103.61, 19.5], [103.61, 5.86], [114.16, 19.5], [117, 19.5], [117, 0.33], [113.66, 0.33], [113.66, 13.59]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[130.03, 3.45], [136.11, 3.45], [136.11, 0.33], [120.55, 0.33], [120.55, 3.45], [126.63, 3.45], [126.63, 19.5], [130.03, 19.5]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.86, 0.33], [139.86, 19.5], [143.2, 19.5], [143.2, 0.33]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0.91, -0.5], [1.04, 0.03], [0.8, 0.36], [0.56, 0.63], [0.29, 0.8], [-0.01, 0.91], [-0.31, 0.84], [-0.57, 0.62], [-0.76, 0.34], [-0.88, -0.01], [-0.91, -0.45], [-0.73, -0.69], [0, 0], [0.51, 0.37], [0.59, 0.26], [0.68, 0.13], [0.8, 0], [1.27, -0.52], [0.89, -0.91], [0.47, -1.18], [0, -1.32], [-0.5, -1.24], [-3.95, 0.04], [-0.81, 0.16], [-0.65, 0.32], [-0.55, 0.41], [-0.48, 0.51]], "o": [[0, 0], [-0.75, 0.71], [-0.93, 0.47], [-0.87, 0.01], [-0.77, -0.36], [-0.58, -0.63], [-0.32, -0.85], [-0.01, -0.9], [0.29, -0.8], [0.57, -0.62], [0.8, -0.36], [1.01, -0.02], [0.9, 0.46], [0, 0], [-0.46, -0.43], [-0.53, -0.37], [-0.64, -0.27], [-0.79, -0.15], [-1.37, -0.03], [-1.18, 0.5], [-0.88, 0.91], [-0.5, 1.23], [-0.01, 1.34], [1.48, 3.66], [0.82, 0], [0.7, -0.14], [0.63, -0.29], [0.55, -0.43], [0, 0]], "v": [[165.04, 16.44], [162.88, 14.25], [160.38, 16.07], [157.38, 16.74], [154.84, 16.21], [152.84, 14.71], [151.52, 12.53], [151.05, 9.86], [151.5, 7.23], [152.81, 5.09], [154.82, 3.63], [157.36, 3.1], [160.29, 3.75], [162.75, 5.48], [164.9, 3], [163.45, 1.79], [161.77, 0.85], [159.8, 0.23], [157.39, 0.02], [153.4, 0.77], [150.27, 2.9], [148.23, 6.07], [147.48, 9.93], [148.23, 13.82], [157.23, 19.82], [159.68, 19.59], [161.72, 18.9], [163.5, 17.85], [165.04, 16.44]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.27], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [0]}, {"t": 66, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "hd": false}]}], "meta": {"g": "LF SVG to Lottie"}, "assets": []}