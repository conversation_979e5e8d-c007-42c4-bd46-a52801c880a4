@font-face {
  font-family: 'icomoon';
  src:  url('icomoon.eot?a5vsav');
  src:  url('icomoon.eot?a5vsav#iefix') format('embedded-opentype'),
    url('icomoon.ttf?a5vsav') format('truetype'),
    url('icomoon.woff?a5vsav') format('woff'),
    url('icomoon.svg?a5vsav#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-acoustic:before {
  content: "\e900";
}
.icon-collaboration:before {
  content: "\e901";
}
.icon-demo:before {
  content: "\e902";
}
.icon-drywall-finish:before {
  content: "\e903";
}
.icon-drywall-install:before {
  content: "\e904";
}
.icon-drywall-texture:before {
  content: "\e905";
}
.icon-integrity:before {
  content: "\e906";
}
.icon-metal:before {
  content: "\e907";
}
.icon-quality:before {
  content: "\e908";
}
.icon-safety:before {
  content: "\e909";
}
