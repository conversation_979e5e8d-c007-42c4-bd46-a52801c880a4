/**
 * Admin Theme Options CSS
 * Custom styling for the WordPress admin theme options page
 */

/* Main Container */
.fcre-admin-theme-options {
    background: #f1f1f1 !important;
    margin: 0 !important;
    margin-left: -20px !important;
}

.fcre-admin-theme-options .wrap {
    margin: 0 !important;
    padding: 0 !important;
}

/* Header */
.fcre-admin-header {
    background: linear-gradient(135deg, #092a63 0%, #51cb3f 100%) !important;
    padding: 30px !important;
    margin: 0 0 30px 0 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

.fcre-admin-header h1 {
    color: white !important;
    font-size: 28px !important;
    margin: 0 0 10px 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.fcre-admin-header p.description {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 16px !important;
    margin: 0 !important;
    max-width: 800px !important;
}

/* Container */
.fcre-admin-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 30px 30px !important;
}

/* Form */
.fcre-options-form {
    margin: 0 !important;
    padding: 0 !important;
}

/* Sections */
.fcre-section {
    background: white !important;
    border-radius: 8px !important;
    padding: 30px !important;
    margin-bottom: 30px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e1e1e1 !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

/* Section specific styling */
.fcre-color-section {
    border-left: 4px solid #092a63 !important;
}

.fcre-theme-section {
    border-left: 4px solid #51cb3f !important;
}

.fcre-visibility-section {
    border-left: 4px solid #ff6b35 !important;
}

.fcre-section h2 {
    color: #092a63 !important;
    font-size: 24px !important;
    margin: 0 0 15px 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    border-bottom: 2px solid #f1f1f1 !important;
    padding-bottom: 15px !important;
    position: relative !important;
}

.fcre-section h2::after {
    content: "" !important;
    position: absolute !important;
    bottom: -2px !important;
    left: 0 !important;
    width: 60px !important;
    height: 2px !important;
    background: linear-gradient(135deg, #092a63 0%, #51cb3f 100%) !important;
}

.fcre-section .section-description {
    color: #666 !important;
    font-size: 14px !important;
    margin: 0 0 25px 0 !important;
    font-style: italic !important;
}

/* Color Picker Group */
.fcre-color-picker-group {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 20px !important;
}

.fcre-color-item {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    padding: 15px !important;
    background: #f9f9f9 !important;
    border-radius: 6px !important;
    border: 1px solid #e1e1e1 !important;
    transition: all 0.3s ease !important;
}

.fcre-color-item label {
    font-weight: 600 !important;
    color: #333 !important;
    min-width: 140px !important;
}

.fcre-color-input {
    width: 60px !important;
    height: 40px !important;
    border: 2px solid #ddd !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.fcre-color-input:hover {
    border-color: #092a63 !important;
    transform: scale(1.05) !important;
}

.color-value {
    font-family: monospace !important;
    background: #333 !important;
    color: white !important;
    padding: 5px 10px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    font-weight: bold !important;
}

/* Option Groups */
.fcre-option-group {
    margin-bottom: 30px !important;
}

.fcre-option-group h3 {
    color: #092a63 !important;
    font-size: 20px !important;
    margin: 0 0 15px 0 !important;
    font-weight: 600 !important;
}

.fcre-sub-group {
    margin-bottom: 20px !important;
}

.fcre-sub-group h4 {
    color: #555 !important;
    font-size: 16px !important;
    margin: 0 0 10px 0 !important;
    font-weight: 500 !important;
}

/* Radio Groups */
.fcre-radio-group {
    display: flex !important;
    gap: 15px !important;
}

.fcre-radio-item {
    position: relative !important;
}

.fcre-radio-item input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

.fcre-radio-item label {
    display: block !important;
    padding: 12px 20px !important;
    background: #f8f8f8 !important;
    border: 2px solid #ddd !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    color: #555 !important;
    min-width: 100px !important;
    text-align: center !important;
}

.fcre-radio-item input[type="radio"]:checked+label {
    background: #092a63 !important;
    color: white !important;
    border-color: #092a63 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(9, 42, 99, 0.3) !important;
}

.fcre-radio-item label:hover {
    border-color: #51cb3f !important;
    background: #f0f8f0 !important;
}

.fcre-radio-item input[type="radio"]:checked+label:hover {
    background: #0a2d6b !important;
    border-color: #0a2d6b !important;
}

/* Banner Options */
.fcre-banner-options {
    margin-top: 25px !important;
}

.fcre-banner-item {
    margin-bottom: 20px !important;
    position: relative !important;
}

.fcre-banner-item:last-child {
    margin-bottom: 0 !important;
}

.fcre-banner-item label {
    display: block !important;
    padding: 25px !important;
    background: #f8f8f8 !important;
    border: 2px solid #ddd !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-align: left !important;
    min-width: auto !important;
    max-width: 100% !important;
    position: relative !important;
    overflow: hidden !important;
}

.fcre-banner-item label:hover {
    background: #f0f8ff !important;
    border-color: #51cb3f !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.fcre-banner-item label strong {
    color: #092a63 !important;
    font-size: 18px !important;
    display: block !important;
    margin-bottom: 10px !important;
    font-weight: 700 !important;
}

.fcre-banner-item label img {
    width: 100% !important;
    max-width: 400px !important;
    height: 160px !important;
    border-radius: 6px !important;
    margin-top: 15px !important;
    border: 2px solid #e1e1e1 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.fcre-banner-item input[type="radio"]:checked+label {
    border-color: #092a63 !important;
    box-shadow: 0 6px 20px rgba(9, 42, 99, 0.2) !important;
    transform: translateY(-3px) !important;
}

.fcre-banner-item input[type="radio"]:checked+label strong {
    color: #51cb3f !important;
}

.fcre-banner-item input[type="radio"]:checked+label img {
    border-color: #51cb3f !important;
    box-shadow: 0 4px 15px rgba(81, 203, 63, 0.3) !important;
}

/* Visibility Section */
.fcre-visibility-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 20px !important;
}

.fcre-visibility-item {
    background: #f9f9f9 !important;
    border: 1px solid #e1e1e1 !important;
    border-radius: 6px !important;
    padding: 20px !important;
    transition: all 0.3s ease !important;
}

.fcre-visibility-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
}

.fcre-visibility-item h4 {
    color: #092a63 !important;
    font-size: 16px !important;
    margin: 0 0 15px 0 !important;
    font-weight: 600 !important;
}

.fcre-toggle-group {
    display: flex !important;
    gap: 10px !important;
}

.fcre-toggle-item {
    flex: 1 !important;
}

.fcre-toggle-item input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

.fcre-toggle-item label {
    display: block !important;
    padding: 10px !important;
    text-align: center !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    font-size: 14px !important;
}

.fcre-toggle-item .toggle-on {
    background: #e8f5e8 !important;
    border: 2px solid #51cb3f !important;
    color: #2d5a2d !important;
}

.fcre-toggle-item .toggle-off {
    background: #ffeaea !important;
    border: 2px solid #dc3545 !important;
    color: #721c24 !important;
}

.fcre-toggle-item input[type="radio"]:checked+.toggle-on {
    background: #51cb3f !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(81, 203, 63, 0.3) !important;
}

.fcre-toggle-item input[type="radio"]:checked+.toggle-off {
    background: #dc3545 !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3) !important;
}

/* Save Button */
.fcre-save-section {
    text-align: center !important;
    margin-top: 40px !important;
}

.fcre-save-button {
    background: linear-gradient(135deg, #51cb3f 0%, #092a63 100%) !important;
    color: white !important;
    border: none !important;
    padding: 15px 40px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 10px !important;
    box-shadow: 0 4px 12px rgba(9, 42, 99, 0.3) !important;
}

.fcre-save-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(9, 42, 99, 0.4) !important;
}

/* Info Section */
.fcre-admin-info {
    background: white !important;
    border-radius: 8px !important;
    padding: 30px !important;
    margin-bottom: 30px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e1e1e1 !important;
}

.fcre-admin-info h3 {
    color: #092a63 !important;
    font-size: 20px !important;
    margin: 0 0 20px 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.fcre-info-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 20px !important;
}

.fcre-info-item h4 {
    color: #51cb3f !important;
    font-size: 16px !important;
    margin: 0 0 8px 0 !important;
    font-weight: 600 !important;
}

.fcre-info-item p {
    color: #666 !important;
    margin: 0 !important;
    font-size: 14px !important;
}

.fcre-note {
    background: #f0f8ff !important;
    border-left: 4px solid #092a63 !important;
    padding: 15px !important;
    margin: 0 !important;
    border-radius: 0 4px 4px 0 !important;
    color: #555 !important;
}

/* Export/Import Section */
.fcre-export-import {
    background: white !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e1e1e1 !important;
    border-left: 4px solid #6c757d !important;
    padding: 30px !important;
    margin-top: 30px !important;
    margin-bottom: 30px !important;
}

.fcre-export-import h3 {
    color: #092a63 !important;
    font-size: 20px !important;
    margin: 0 0 20px 0 !important;
    font-weight: 600 !important;
}

.fcre-export-import h4 {
    color: #51cb3f !important;
    font-size: 16px !important;
    margin: 0 0 10px 0 !important;
    font-weight: 600 !important;
}

.fcre-export-import p {
    color: #666 !important;
    font-size: 14px !important;
    margin: 0 0 15px 0 !important;
}

.fcre-export-import .button {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    color: white !important;
    border: none !important;
    padding: 10px 20px !important;
    border-radius: 5px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
}

.fcre-export-import .button:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3) !important;
    color: white !important;
}

.fcre-export-import input[type="file"] {
    padding: 8px !important;
    border: 2px solid #ddd !important;
    border-radius: 4px !important;
    background: #f8f9fa !important;
    width: 100% !important;
    max-width: 300px !important;
    font-size: 14px !important;
}

.fcre-export-import input[type="file"]:focus {
    border-color: #092a63 !important;
    outline: none !important;
    box-shadow: 0 0 0 0.2rem rgba(9, 42, 99, 0.25) !important;
}

/* Additional hover states and animations */
.fcre-section:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px) !important;
}

.fcre-color-item:hover {
    background: #f0f8ff !important;
    border-color: #092a63 !important;
}

.fcre-visibility-item.hover {
    background: #f0f8ff !important;
    border-color: #51cb3f !important;
}

/* Loading states */
.fcre-save-button:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

/* Focus states for accessibility */
.fcre-color-input:focus,
.fcre-radio-item input[type="radio"]:focus+label,
.fcre-toggle-item input[type="radio"]:focus+label {
    outline: 2px solid #092a63 !important;
    outline-offset: 2px !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .fcre-admin-container {
        max-width: 100% !important;
        padding: 0 20px 20px !important;
    }

    .fcre-banner-item label {
        max-width: 100% !important;
    }

    .fcre-banner-item label img {
        max-width: 100% !important;
    }
}

@media (max-width: 768px) {
    .fcre-admin-container {
        padding: 0 15px 15px !important;
    }

    .fcre-admin-header {
        padding: 20px 15px !important;
    }

    .fcre-admin-header h1 {
        font-size: 24px !important;
    }

    .fcre-section {
        padding: 20px !important;
        margin-bottom: 20px !important;
    }

    .fcre-section h2 {
        font-size: 20px !important;
    }

    .fcre-color-picker-group {
        grid-template-columns: 1fr !important;
    }

    .fcre-color-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }

    .fcre-radio-group {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .fcre-radio-item label {
        min-width: auto !important;
        width: 100% !important;
    }

    .fcre-visibility-grid {
        grid-template-columns: 1fr !important;
    }

    .fcre-banner-item label {
        padding: 20px !important;
    }

    .fcre-banner-item label strong {
        font-size: 16px !important;
    }

    .fcre-save-button {
        width: 100% !important;
        padding: 18px 20px !important;
        font-size: 16px !important;
    }

    .fcre-export-import {
        padding: 20px !important;
    }

    .fcre-export-import>div {
        flex-direction: column !important;
        gap: 20px !important;
    }

    .fcre-export-import>div>div {
        min-width: auto !important;
    }
}

@media (max-width: 480px) {
    .fcre-admin-header {
        padding: 15px !important;
    }

    .fcre-admin-header h1 {
        font-size: 20px !important;
        flex-direction: column !important;
        gap: 5px !important;
    }

    .fcre-section {
        padding: 15px !important;
    }

    .fcre-color-item {
        padding: 12px !important;
    }

    .fcre-toggle-group {
        flex-direction: column !important;
        gap: 8px !important;
    }

    .fcre-banner-item label {
        padding: 15px !important;
    }
}